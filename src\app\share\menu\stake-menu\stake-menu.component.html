<!-- 容器 -->
<div class="stake-menu-container">
  <!-- 搜索条件区域 -->
  <div class="search-conditions-container" *ngIf="showSearch">
    <!-- 桩类型选择 -->
    <div class="condition-item">
      <ion-label class="condition-label">桩类型：</ion-label>
      <div class="condition-control">
        <ost-quick-option-select
          [isShowIcon]="false"
          [(ngModel)]="stakeType"
          [labelValue]="'dictValue'"
          [labelName]="'dictValue'"
          interfaceUrl="/work-basic/api/v2/basic/dict/msg/list?dictCode=fcMarkerType"
          (ngModelChange)="onStakeTypeChange()"
        ></ost-quick-option-select>
      </div>
    </div>

    <!-- 关键词搜索 -->
    <div class="condition-item">
      <ion-label class="condition-label">关键词：</ion-label>
      <div class="condition-control search-input-wrapper">
        <ion-icon class="search-icon" name="search-outline"></ion-icon>
        <ion-input
          class="search-input"
          [placeholder]="searchPlaceholder"
          [(ngModel)]="params"
          (ionInput)="onSearchInput()"
        ></ion-input>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="search-actions">
      <ion-button
        fill="clear"
        size="small"
        color="medium"
        (click)="onReset()"
        class="action-button reset-button">
        <ion-icon name="refresh-outline" slot="start"></ion-icon>
        重置
      </ion-button>
      <ion-button
        fill="solid"
        size="small"
        color="primary"
        (click)="onSearch()"
        class="action-button search-button">
        <ion-icon name="search-outline" slot="start"></ion-icon>
        搜索
      </ion-button>
    </div>
  </div>

  <!-- 内容区域 - 使用 ion-content 支持无限滚动 -->
  <ion-content class="content-scrollable" [style.height.px]="contentHeight" [scrollEvents]="true">
    <!-- 优化的加载状态 -->
    <div *ngIf="loading" class="loading-container">
      <div class="loading-content">
        <ion-spinner name="bubbles" color="primary"></ion-spinner>
        <span class="loading-text">{{ loadingText }}</span>
      </div>
    </div>

    <div *ngIf="items.length<=0 && !loading;" class="no-data">
      <img src="assets/menu/box2.png" style="padding-top: 50px;" />
      <!-- 暂无数据 -->
      <span class="no-data-span">暂无数据</span>
    </div>

    <ost-tree-list
      *ngIf="items.length > 0"
      #menu
      [items]="items"
      (toggleSubMenu)="onToggleSubMenu($event)"
      (itemClick)="onItemClick($event)">
    </ost-tree-list>

    <!-- 加载更多 -->
    <ion-infinite-scroll *ngIf="isPage" threshold="50px" (ionInfinite)="loadMoreData($event)">
      <ion-infinite-scroll-content
        [loadingSpinner]="spinner"
        [loadingText]="isShowNoMoreStr">
      </ion-infinite-scroll-content>
    </ion-infinite-scroll>
  </ion-content>
</div>