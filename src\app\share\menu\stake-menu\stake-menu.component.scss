// 容器样式
.stake-menu-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

// 紧凑搜索容器样式
.compact-search-container {
  position: sticky;
  top: 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  z-index: 100;
  flex-shrink: 0;
  padding: 10px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

  // 搜索行样式
  .search-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    // 搜索项样式
    .search-item {
      flex: 1;
      display: flex;
      align-items: center;
      margin-right: 12px;

      .search-label {
        font-size: 14px;
        font-weight: 500;
        color: #495057;
        margin-right: 8px;
        min-width: 55px;
        flex-shrink: 0;
      }

      // 桩类型选择器
      ost-quick-option-select {
        flex: 1;
        max-width: 140px;

        // 优化选择器内部样式
        ::ng-deep {
          .quick-radio-group {
            gap: 8px;

            ion-item-divider {
              background-color: #ffffff;
              border: 1px solid #dee2e6;
              border-radius: 6px;
              padding: 4px 8px;
              transition: all 0.2s ease;

              &:hover {
                border-color: #007bff;
                box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
              }

              ion-label {
                font-size: 12px;
                color: #495057;
              }
            }
          }

          // 列表选择模式样式优化
          .input-search {
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 6px 10px;
            transition: border-color 0.2s ease;

            &:focus-within {
              border-color: #007bff;
              box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
            }

            ion-input {
              font-size: 13px;
              --color: #495057;
              --placeholder-color: #6c757d;
            }
          }
        }
      }

      // 关键词输入框特殊样式
      &.search-input-item {
        .compact-search-input {
          flex: 1;
          --padding-start: 10px;
          --padding-end: 10px;
          --color: #495057;
          --placeholder-color: #6c757d;
          font-size: 13px;
          height: 36px;
          border: 1px solid #dee2e6;
          border-radius: 6px;
          background-color: #ffffff;
          transition: border-color 0.2s ease, box-shadow 0.2s ease;

          &:focus-within {
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
          }
        }
      }
    }

    // 搜索触发图标
    .search-trigger-icon {
      font-size: 22px;
      color: #007bff;
      cursor: pointer;
      padding: 8px;
      border-radius: 50%;
      background-color: #ffffff;
      border: 1px solid #dee2e6;
      transition: all 0.2s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      &:hover {
        background-color: #007bff;
        color: #ffffff;
        transform: scale(1.05);
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.2);
      }

      &:active {
        transform: scale(0.95);
      }
    }

    // 重置链接
    .reset-link {
      font-size: 13px;
      font-weight: 500;
      color: #6c757d;
      cursor: pointer;
      padding: 6px 12px;
      border-radius: 6px;
      background-color: #ffffff;
      border: 1px solid #dee2e6;
      transition: all 0.2s ease;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

      &:hover {
        color: #495057;
        background-color: #f8f9fa;
        border-color: #adb5bd;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

// 可滚动内容区域样式
.content-scrollable {
  flex: 1;
  position: relative;

  // 重置 ion-content 的默认样式
  --padding-top: 0;
  --padding-bottom: 0;
  --padding-start: 0;
  --padding-end: 0;
}

// 加载状态样式
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  min-height: 120px;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    ion-spinner {
      width: 32px;
      height: 32px;
    }

    .loading-text {
      color: #666;
      font-size: 14px;
      font-weight: 400;
    }
  }
}

// 无数据状态样式
.no-data {
  text-align: center;
  padding: 20px;

  .no-data-span {
    display: block;
    margin-top: 10px;
    color: #999;
    font-size: 14px;
  }
}

.no-data {
  text-align: center;
  padding: 20px;

  .no-data-span {
    display: block;
    margin-top: 10px;
    color: #999;
    font-size: 14px;
  }
}