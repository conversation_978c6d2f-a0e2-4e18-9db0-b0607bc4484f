// 容器样式
.stake-menu-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

// 紧凑搜索容器样式
.compact-search-container {
  position: sticky;
  top: 0;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  z-index: 100;
  flex-shrink: 0;
  padding: 8px 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  // 搜索行样式
  .search-row {
    display: flex;
    align-items: center;
    margin-bottom: 6px;

    &:last-child {
      margin-bottom: 0;
    }

    // 搜索项样式
    .search-item {
      flex: 1;
      display: flex;
      align-items: center;
      margin-right: 8px;

      .search-label {
        font-size: 13px;
        color: #495057;
        margin-right: 6px;
        min-width: 50px;
        flex-shrink: 0;
      }

      // 桩类型选择器
      ost-quick-option-select {
        flex: 1;
        max-width: 120px;
      }

      // 关键词输入框特殊样式
      &.search-input-item {
        .compact-search-input {
          flex: 1;
          --padding-start: 8px;
          --padding-end: 8px;
          --color: #495057;
          --placeholder-color: #6c757d;
          font-size: 13px;
          height: 32px;
          border: 1px solid #dee2e6;
          border-radius: 4px;
          background-color: #ffffff;
        }
      }
    }

    // 搜索触发图标
    .search-trigger-icon {
      font-size: 20px;
      color: #007bff;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: rgba(0, 123, 255, 0.1);
      }
    }

    // 重置链接
    .reset-link {
      font-size: 13px;
      color: #6c757d;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      transition: color 0.2s ease;

      &:hover {
        color: #495057;
        background-color: rgba(108, 117, 125, 0.1);
      }
    }
  }
}

// 可滚动内容区域样式
.content-scrollable {
  flex: 1;
  position: relative;

  // 重置 ion-content 的默认样式
  --padding-top: 0;
  --padding-bottom: 0;
  --padding-start: 0;
  --padding-end: 0;
}

// 加载状态样式
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  min-height: 120px;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    ion-spinner {
      width: 32px;
      height: 32px;
    }

    .loading-text {
      color: #666;
      font-size: 14px;
      font-weight: 400;
    }
  }
}

// 无数据状态样式
.no-data {
  text-align: center;
  padding: 20px;

  .no-data-span {
    display: block;
    margin-top: 10px;
    color: #999;
    font-size: 14px;
  }
}

.no-data {
  text-align: center;
  padding: 20px;

  .no-data-span {
    display: block;
    margin-top: 10px;
    color: #999;
    font-size: 14px;
  }
}