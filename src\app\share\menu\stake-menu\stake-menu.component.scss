// 容器样式
.stake-menu-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

// 搜索条件容器样式
.search-conditions-container {
  position: sticky;
  top: 0;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  z-index: 100;
  flex-shrink: 0;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  // 条件项样式
  .condition-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    &:last-of-type {
      margin-bottom: 16px;
    }

    .condition-label {
      min-width: 60px;
      font-size: 14px;
      font-weight: 500;
      color: #495057;
      margin-right: 12px;
      flex-shrink: 0;
    }

    .condition-control {
      flex: 1;

      // 桩类型选择器样式
      ost-quick-option-select {
        width: 100%;
      }
    }
  }

  // 搜索输入框包装器
  .search-input-wrapper {
    display: flex;
    align-items: center;
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 0 12px;
    height: 40px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;

    &:focus-within {
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
    }

    .search-icon {
      color: #6c757d;
      font-size: 16px;
      margin-right: 8px;
      flex-shrink: 0;
    }

    .search-input {
      flex: 1;
      --padding-start: 0;
      --padding-end: 0;
      --color: #495057;
      --placeholder-color: #6c757d;
      font-size: 14px;
    }
  }

  // 操作按钮区域
  .search-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 4px;

    .action-button {
      height: 36px;
      font-size: 13px;
      font-weight: 500;
      --border-radius: 6px;

      ion-icon {
        font-size: 14px;
      }
    }

    .reset-button {
      --color: #6c757d;
      --color-hover: #495057;
    }

    .search-button {
      min-width: 80px;
    }
  }
}

// 可滚动内容区域样式
.content-scrollable {
  flex: 1;
  position: relative;

  // 重置 ion-content 的默认样式
  --padding-top: 0;
  --padding-bottom: 0;
  --padding-start: 0;
  --padding-end: 0;
}

// 加载状态样式
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  min-height: 120px;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    ion-spinner {
      width: 32px;
      height: 32px;
    }

    .loading-text {
      color: #666;
      font-size: 14px;
      font-weight: 400;
    }
  }
}

// 无数据状态样式
.no-data {
  text-align: center;
  padding: 20px;

  .no-data-span {
    display: block;
    margin-top: 10px;
    color: #999;
    font-size: 14px;
  }
}

.no-data {
  text-align: center;
  padding: 20px;

  .no-data-span {
    display: block;
    margin-top: 10px;
    color: #999;
    font-size: 14px;
  }
}